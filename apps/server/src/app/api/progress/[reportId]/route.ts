import { NextRequest, NextResponse } from 'next/server';
import { createServerClient } from "@cma/database";

export async function GET(
  request: NextRequest,
  { params }: { params: { reportId: string } }
) {
  const { reportId } = params;
  
  if (!reportId) {
    return new NextResponse('Report ID is required', { status: 400 });
  }

  // Set up Server-Sent Events
  const encoder = new TextEncoder();
  const stream = new ReadableStream({
    start(controller) {
      // Send initial connection message
      const data = `data: ${JSON.stringify({ type: 'connected', reportId })}\n\n`;
      controller.enqueue(encoder.encode(data));

      // Set up polling for progress updates
      const pollProgress = async () => {
        try {
          const db = createServerClient();
          
          // Get report status
          const { data: report } = await db
            .from('reports')
            .select('*')
            .eq('id', reportId)
            .single();

          if (!report) {
            const errorData = `data: ${JSON.stringify({ 
              type: 'error', 
              message: 'Report not found' 
            })}\n\n`;
            controller.enqueue(encoder.encode(errorData));
            return;
          }

          // Get agent runs
          const { data: agentRuns } = await db
            .from('agent_runs')
            .select('*')
            .eq('report_id', reportId)
            .order('created_at', { ascending: true });

          // Calculate overall progress
          const totalAgents = 6; // Expected number of agents
          const completedAgents = (agentRuns || []).filter(run => run.status === 'completed').length;
          const runningAgents = (agentRuns || []).filter(run => run.status === 'running').length;
          const overallProgress = Math.round(((completedAgents + runningAgents * 0.5) / totalAgents) * 100);

          // Send progress update
          const progressData = {
            type: 'progress',
            reportId,
            report: {
              id: report.id,
              status: report.status,
              title: report.title,
              created_at: report.created_at,
              updated_at: report.updated_at,
              completed_at: report.completed_at,
            },
            agentRuns: agentRuns || [],
            overallProgress,
            timestamp: new Date().toISOString(),
          };

          const data = `data: ${JSON.stringify(progressData)}\n\n`;
          controller.enqueue(encoder.encode(data));

          // If analysis is complete or failed, stop polling
          if (report.status === 'completed' || report.status === 'failed') {
            const finalData = `data: ${JSON.stringify({ 
              type: 'finished', 
              status: report.status,
              reportId 
            })}\n\n`;
            controller.enqueue(encoder.encode(finalData));
            controller.close();
            return;
          }

        } catch (error) {
          console.error('Error polling progress:', error);
          const errorData = `data: ${JSON.stringify({ 
            type: 'error', 
            message: 'Failed to fetch progress' 
          })}\n\n`;
          controller.enqueue(encoder.encode(errorData));
        }
      };

      // Initial poll
      pollProgress();

      // Set up interval for polling
      const interval = setInterval(pollProgress, 2000); // Poll every 2 seconds

      // Cleanup function
      const cleanup = () => {
        clearInterval(interval);
        controller.close();
      };

      // Handle client disconnect
      request.signal.addEventListener('abort', cleanup);

      // Auto-cleanup after 10 minutes
      setTimeout(cleanup, 10 * 60 * 1000);
    },
  });

  return new NextResponse(stream, {
    headers: {
      'Content-Type': 'text/event-stream',
      'Cache-Control': 'no-cache',
      'Connection': 'keep-alive',
      'Access-Control-Allow-Origin': '*',
      'Access-Control-Allow-Methods': 'GET',
      'Access-Control-Allow-Headers': 'Content-Type',
    },
  });
}

// Handle preflight requests
export async function OPTIONS() {
  return new NextResponse(null, {
    status: 200,
    headers: {
      'Access-Control-Allow-Origin': '*',
      'Access-Control-Allow-Methods': 'GET',
      'Access-Control-Allow-Headers': 'Content-Type',
    },
  });
}
