import {
  publicProcedure,
  router,
} from "../lib/trpc";
import { z } from "zod";
import { AgentOrchestrator } from "@cma/agents";
import { ProjectInputSchema } from "@cma/ai";
import { createServerClient } from "@cma/database";

const db = createServerClient();

export const appRouter = router({
  healthCheck: publicProcedure.query(() => {
    return "OK";
  }),

  // Start analysis for a project
  startAnalysis: publicProcedure
    .input(ProjectInputSchema)
    .mutation(async ({ input }) => {
      try {
        console.log("🚀 Starting analysis for:", input);

        // Create or find project
        const project = await createOrFindProject(input);

        // Create report
        const report = await createReport(project.id, input.reportDepth);

        // Start orchestration (async)
        const orchestrator = new AgentOrchestrator();
        const context = {
          reportId: report.id,
          projectId: project.id,
          workspaceId: project.workspace_id,
          userId: project.created_by,
          reportDepth: input.reportDepth,
          projectData: project,
        };

        // Start analysis in background
        orchestrator.orchestrateResearch(context, input)
          .then((result) => {
            console.log("✅ Analysis completed:", result.success);
          })
          .catch((error) => {
            console.error("❌ Analysis failed:", error);
          });

        return {
          success: true,
          reportId: report.id,
          projectId: project.id,
          message: "Analysis started successfully",
        };

      } catch (error) {
        console.error("Failed to start analysis:", error);
        throw new Error("Failed to start analysis: " + (error instanceof Error ? error.message : "Unknown error"));
      }
    }),

  // Get analysis progress
  getAnalysisProgress: publicProcedure
    .input(z.object({ reportId: z.string() }))
    .query(async ({ input }) => {
      try {
        const { data: report } = await db
          .from("reports")
          .select("*")
          .eq("id", input.reportId)
          .single();

        if (!report) {
          throw new Error("Report not found");
        }

        const { data: agentRuns } = await db
          .from("agent_runs")
          .select("*")
          .eq("report_id", input.reportId)
          .order("created_at", { ascending: true });

        return {
          report,
          agentRuns: agentRuns || [],
          progress: calculateOverallProgress(agentRuns || []),
        };

      } catch (error) {
        console.error("Failed to get analysis progress:", error);
        throw new Error("Failed to get analysis progress");
      }
    }),

  // Get completed report
  getReport: publicProcedure
    .input(z.object({ reportId: z.string() }))
    .query(async ({ input }) => {
      try {
        const { data: report } = await db
          .from("reports")
          .select(`
            *,
            projects (
              id,
              name,
              type,
              identifier,
              website,
              twitter_handle,
              github_url
            )
          `)
          .eq("id", input.reportId)
          .single();

        if (!report) {
          throw new Error("Report not found");
        }

        return report;

      } catch (error) {
        console.error("Failed to get report:", error);
        throw new Error("Failed to get report");
      }
    }),

  // List user's reports
  listReports: publicProcedure
    .input(z.object({
      userId: z.string().optional(),
      limit: z.number().default(10),
      offset: z.number().default(0),
    }))
    .query(async ({ input }) => {
      try {
        let query = db
          .from("reports")
          .select(`
            *,
            projects (
              id,
              name,
              type,
              identifier
            )
          `)
          .order("created_at", { ascending: false })
          .range(input.offset, input.offset + input.limit - 1);

        if (input.userId) {
          query = query.eq("generated_by", input.userId);
        }

        const { data: reports } = await query;

        return {
          reports: reports || [],
          hasMore: (reports?.length || 0) === input.limit,
        };

      } catch (error) {
        console.error("Failed to list reports:", error);
        throw new Error("Failed to list reports");
      }
    }),
});

export type AppRouter = typeof appRouter;

// Helper functions
async function createOrFindProject(input: any) {
  // Try to find existing project
  const { data: existingProject } = await db
    .from("projects")
    .select("*")
    .eq("identifier", input.value)
    .eq("type", input.type.toUpperCase())
    .single();

  if (existingProject) {
    return existingProject;
  }

  // Create new project
  const projectData = {
    name: extractProjectName(input),
    type: input.type.toUpperCase(),
    identifier: input.value,
    description: `Project discovered from ${input.type}: ${input.value}`,
    website: input.type === "domain" ? `https://${input.value}` : null,
    twitter_handle: input.type === "twitter" ? input.value : null,
    created_by: "system", // TODO: Use actual user ID
    workspace_id: "default", // TODO: Use actual workspace ID
  };

  const { data: newProject } = await db
    .from("projects")
    .insert(projectData)
    .select()
    .single();

  return newProject;
}

async function createReport(projectId: string, depth: string) {
  const reportData = {
    project_id: projectId,
    title: `CMA Report - ${new Date().toLocaleDateString()}`,
    status: "pending",
    depth: depth.toUpperCase(),
    generated_by: "system", // TODO: Use actual user ID
    workspace_id: "default", // TODO: Use actual workspace ID
  };

  const { data: report } = await db
    .from("reports")
    .insert(reportData)
    .select()
    .single();

  return report;
}

function extractProjectName(input: any): string {
  if (input.type === "domain") {
    return input.value.split(".")[0];
  }
  if (input.type === "twitter") {
    return input.value.replace("@", "");
  }
  return input.value;
}

function calculateOverallProgress(agentRuns: any[]): number {
  if (agentRuns.length === 0) return 0;

  const totalAgents = 6; // Expected number of agents
  const completedAgents = agentRuns.filter(run => run.status === "completed").length;
  const runningAgents = agentRuns.filter(run => run.status === "running").length;

  return Math.round(((completedAgents + runningAgents * 0.5) / totalAgents) * 100);
}
