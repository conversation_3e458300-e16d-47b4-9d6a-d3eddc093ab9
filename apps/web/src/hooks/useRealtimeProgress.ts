import { useEffect, useState, useRef } from 'react';

interface ProgressData {
  type: 'connected' | 'progress' | 'finished' | 'error';
  reportId?: string;
  report?: {
    id: string;
    status: string;
    title: string;
    created_at: string;
    updated_at: string;
    completed_at?: string;
  };
  agentRuns?: Array<{
    id: string;
    agent_type: string;
    status: string;
    metrics?: any;
    started_at?: string;
    completed_at?: string;
  }>;
  overallProgress?: number;
  timestamp?: string;
  message?: string;
  status?: string;
}

interface UseRealtimeProgressReturn {
  data: ProgressData | null;
  isConnected: boolean;
  error: string | null;
  reconnect: () => void;
}

export function useRealtimeProgress(reportId: string): UseRealtimeProgressReturn {
  const [data, setData] = useState<ProgressData | null>(null);
  const [isConnected, setIsConnected] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const eventSourceRef = useRef<EventSource | null>(null);
  const reconnectTimeoutRef = useRef<NodeJS.Timeout | null>(null);

  const connect = () => {
    if (!reportId) return;

    // Close existing connection
    if (eventSourceRef.current) {
      eventSourceRef.current.close();
    }

    try {
      console.log(`🔌 Connecting to SSE for report ${reportId}`);
      
      const eventSource = new EventSource(
        `${process.env.NEXT_PUBLIC_SERVER_URL}/api/progress/${reportId}`
      );
      
      eventSourceRef.current = eventSource;

      eventSource.onopen = () => {
        console.log('✅ SSE connection opened');
        setIsConnected(true);
        setError(null);
      };

      eventSource.onmessage = (event) => {
        try {
          const progressData: ProgressData = JSON.parse(event.data);
          console.log('📊 Progress update:', progressData);
          
          setData(progressData);

          // Handle different message types
          switch (progressData.type) {
            case 'connected':
              console.log('🔗 Connected to progress stream');
              break;
            case 'progress':
              // Update progress data
              break;
            case 'finished':
              console.log('🏁 Analysis finished:', progressData.status);
              // Keep connection open briefly to show final state
              setTimeout(() => {
                eventSource.close();
                setIsConnected(false);
              }, 2000);
              break;
            case 'error':
              console.error('❌ Progress error:', progressData.message);
              setError(progressData.message || 'Unknown error');
              break;
          }
        } catch (err) {
          console.error('Failed to parse SSE data:', err);
          setError('Failed to parse progress data');
        }
      };

      eventSource.onerror = (event) => {
        console.error('❌ SSE connection error:', event);
        setIsConnected(false);
        setError('Connection error');
        
        // Attempt to reconnect after a delay
        if (eventSource.readyState === EventSource.CLOSED) {
          console.log('🔄 Attempting to reconnect in 3 seconds...');
          reconnectTimeoutRef.current = setTimeout(() => {
            connect();
          }, 3000);
        }
      };

    } catch (err) {
      console.error('Failed to create SSE connection:', err);
      setError('Failed to establish connection');
    }
  };

  const reconnect = () => {
    console.log('🔄 Manual reconnect requested');
    connect();
  };

  useEffect(() => {
    if (reportId) {
      connect();
    }

    return () => {
      console.log('🧹 Cleaning up SSE connection');
      if (eventSourceRef.current) {
        eventSourceRef.current.close();
      }
      if (reconnectTimeoutRef.current) {
        clearTimeout(reconnectTimeoutRef.current);
      }
    };
  }, [reportId]);

  // Cleanup on unmount
  useEffect(() => {
    return () => {
      if (eventSourceRef.current) {
        eventSourceRef.current.close();
      }
      if (reconnectTimeoutRef.current) {
        clearTimeout(reconnectTimeoutRef.current);
      }
    };
  }, []);

  return {
    data,
    isConnected,
    error,
    reconnect,
  };
}

// Helper hook for extracting specific progress information
export function useProgressMetrics(progressData: ProgressData | null) {
  if (!progressData || progressData.type !== 'progress') {
    return {
      overallProgress: 0,
      agentProgress: [],
      reportStatus: 'pending',
      isComplete: false,
    };
  }

  const agentTypes = [
    { key: 'lead_research', name: 'Lead Research' },
    { key: 'onchain_analytics', name: 'On-Chain Analytics' },
    { key: 'social_sentiment', name: 'Social Sentiment' },
    { key: 'competitor_analysis', name: 'Competitor Analysis' },
    { key: 'technical_assessment', name: 'Technical Assessment' },
    { key: 'tokenomics_analysis', name: 'Tokenomics Analysis' },
    { key: 'market_positioning', name: 'Market Positioning' },
  ];

  const agentProgress = agentTypes.map(agent => {
    const run = progressData.agentRuns?.find(r => r.agent_type === agent.key);
    return {
      name: agent.name,
      key: agent.key,
      status: run?.status || 'pending',
      progress: run?.metrics?.progress || 0,
      step: run?.metrics?.step || 'pending',
      message: run?.metrics?.message || '',
      startedAt: run?.started_at,
      completedAt: run?.completed_at,
    };
  });

  return {
    overallProgress: progressData.overallProgress || 0,
    agentProgress,
    reportStatus: progressData.report?.status || 'pending',
    isComplete: progressData.report?.status === 'completed',
    isFailed: progressData.report?.status === 'failed',
  };
}
