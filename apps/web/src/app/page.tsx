"use client"
import { useState } from "react";
import { useQuery } from "@tanstack/react-query";
import { useRouter } from "next/navigation";
import { trpc } from "@/utils/trpc";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Input } from "@/components/ui/input";
import { Button } from "@/components/ui/button";
import { Label } from "@/components/ui/label";
import { Skeleton } from "@/components/ui/skeleton";
import Loader from "@/components/loader";
import { toast } from "sonner";

export default function Home() {
  const [input, setInput] = useState("");
  const [isAnalyzing, setIsAnalyzing] = useState(false);
  const router = useRouter();
  const { data: healthCheck, isLoading: healthCheckLoading } = useQuery({
    queryKey: ['healthCheck'],
    queryFn: () => trpc.healthCheck.query(),
  });
  
  const handleAnalysis = async () => {
    if (!input.trim()) return;

    setIsAnalyzing(true);
    try {
      console.log("🚀 Starting analysis for:", input);

      // Determine input type
      const inputType = getInputType(input);

      // Call the analysis API
      const result = await trpc.startAnalysis.mutate({
        type: inputType,
        value: input.trim(),
        reportDepth: 'standard',
      });

      if (result.success) {
        console.log("✅ Analysis started successfully:", result);
        toast.success("Analysis started successfully!", {
          description: "Redirecting to progress page...",
        });

        // Navigate to progress page
        router.push(`/analysis/${result.reportId}`);
      } else {
        throw new Error("Failed to start analysis");
      }

    } catch (error) {
      console.error("❌ Analysis failed:", error);
      toast.error("Failed to start analysis", {
        description: "Please check your input and try again.",
      });
    } finally {
      setIsAnalyzing(false);
    }
  };

  const getInputType = (input: string): 'domain' | 'twitter' | 'contract' => {
    const urlRegex = /^https?:\/\//;
    const domainRegex = /^[\w.-]+\.[a-zA-Z]{2,}$/;
    const twitterRegex = /^@?[\w]+$/;
    const contractRegex = /^0x[a-fA-F0-9]{40}$/;

    if (contractRegex.test(input)) return 'contract';
    if (urlRegex.test(input) || domainRegex.test(input)) return 'domain';
    if (twitterRegex.test(input)) return 'twitter';

    // Default to domain if unclear
    return 'domain';
  };

  const isValidInput = (input: string) => {
    const urlRegex = /^https?:\/\//;
    const domainRegex = /^[\w.-]+\.[a-zA-Z]{2,}$/;
    const twitterRegex = /^@?[\w]+$/;
    
    return urlRegex.test(input) || domainRegex.test(input) || twitterRegex.test(input);
  };

  return (
    <div className="container mx-auto max-w-6xl px-4 py-8 space-y-8">
      {/* Hero Section */}
      <div className="text-center space-y-4">
        <h1 className="text-4xl font-bold tracking-tight">
          Web3 Competitive Market Analysis
        </h1>
        <p className="text-xl text-muted-foreground max-w-2xl mx-auto">
          Comprehensive AI-powered analysis of Web3 projects using multi-agent intelligence. 
          Just enter a domain or Twitter handle to get started.
        </p>
      </div>

      {/* Input Section */}
      <Card className="max-w-2xl mx-auto">
        <CardHeader>
          <CardTitle>Analyze a Web3 Project</CardTitle>
          <CardDescription>
            Enter a website URL, domain, or Twitter handle to start your analysis
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="space-y-2">
            <Label htmlFor="project-input">Project Input</Label>
            <Input
              id="project-input"
              placeholder="e.g., uniswap.org, @Uniswap, https://aave.com"
              value={input}
              onChange={(e) => setInput(e.target.value)}
              className="text-lg"
            />
          </div>
          
          <Button 
            onClick={handleAnalysis}
            disabled={!isValidInput(input) || isAnalyzing}
            className="w-full"
            size="lg"
          >
            {isAnalyzing ? (
              <>
                <Loader className="mr-2 h-4 w-4" />
                Analyzing Project...
              </>
            ) : (
              "Start Analysis"
            )}
          </Button>
          
          {input && !isValidInput(input) && (
            <p className="text-sm text-destructive">
              Please enter a valid URL, domain, or Twitter handle
            </p>
          )}
        </CardContent>
      </Card>

      {/* Features Grid */}
      <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-6">
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              🔍 Smart Discovery
            </CardTitle>
          </CardHeader>
          <CardContent>
            <p className="text-sm text-muted-foreground">
              Automatically discovers project information from minimal input using advanced web scraping and AI analysis.
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              🤖 Multi-Agent Analysis
            </CardTitle>
          </CardHeader>
          <CardContent>
            <p className="text-sm text-muted-foreground">
              Six specialized AI agents work in parallel to analyze different aspects: technical, social, market, and more.
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              📊 Real-Time Tracking
            </CardTitle>
          </CardHeader>
          <CardContent>
            <p className="text-sm text-muted-foreground">
              Watch your analysis progress in real-time with live updates from each specialized agent.
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              📈 On-Chain Data
            </CardTitle>
          </CardHeader>
          <CardContent>
            <p className="text-sm text-muted-foreground">
              Comprehensive blockchain analytics including TVL, volume, fees, and token metrics from DeFiLlama and other sources.
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              💬 Social Sentiment
            </CardTitle>
          </CardHeader>
          <CardContent>
            <p className="text-sm text-muted-foreground">
              Analyzes community engagement across Twitter, Discord, and Telegram with sentiment analysis.
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              📋 Professional Reports
            </CardTitle>
          </CardHeader>
          <CardContent>
            <p className="text-sm text-muted-foreground">
              Generates comprehensive reports with executive summaries, detailed analysis, and actionable recommendations.
            </p>
          </CardContent>
        </Card>
      </div>

      {/* Analysis Agents Overview */}
      <Card>
        <CardHeader>
          <CardTitle>Analysis Dimensions</CardTitle>
          <CardDescription>
            Our multi-agent system analyzes projects across these key dimensions
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="grid md:grid-cols-2 gap-4">
            <div className="space-y-3">
              <div className="flex items-start gap-3">
                <div className="w-2 h-2 rounded-full bg-blue-500 mt-2" />
                <div>
                  <h4 className="font-medium">Lead Research</h4>
                  <p className="text-sm text-muted-foreground">
                    Project discovery, team analysis, and initial assessment
                  </p>
                </div>
              </div>
              
              <div className="flex items-start gap-3">
                <div className="w-2 h-2 rounded-full bg-green-500 mt-2" />
                <div>
                  <h4 className="font-medium">On-Chain Analytics</h4>
                  <p className="text-sm text-muted-foreground">
                    TVL, volume, transactions, and blockchain metrics
                  </p>
                </div>
              </div>
              
              <div className="flex items-start gap-3">
                <div className="w-2 h-2 rounded-full bg-purple-500 mt-2" />
                <div>
                  <h4 className="font-medium">Social Sentiment</h4>
                  <p className="text-sm text-muted-foreground">
                    Community engagement and sentiment analysis
                  </p>
                </div>
              </div>
            </div>
            
            <div className="space-y-3">
              <div className="flex items-start gap-3">
                <div className="w-2 h-2 rounded-full bg-orange-500 mt-2" />
                <div>
                  <h4 className="font-medium">Technical Assessment</h4>
                  <p className="text-sm text-muted-foreground">
                    Code quality, security, and technical architecture
                  </p>
                </div>
              </div>
              
              <div className="flex items-start gap-3">
                <div className="w-2 h-2 rounded-full bg-red-500 mt-2" />
                <div>
                  <h4 className="font-medium">Tokenomics Analysis</h4>
                  <p className="text-sm text-muted-foreground">
                    Token distribution, utility, and economic model
                  </p>
                </div>
              </div>
              
              <div className="flex items-start gap-3">
                <div className="w-2 h-2 rounded-full bg-indigo-500 mt-2" />
                <div>
                  <h4 className="font-medium">Market Positioning</h4>
                  <p className="text-sm text-muted-foreground">
                    Competitive landscape and strategic recommendations
                  </p>
                </div>
              </div>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* System Status */}
      <Card className="max-w-md mx-auto">
        <CardHeader>
          <CardTitle>System Status</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="flex items-center gap-2">
            {healthCheckLoading ? (
              <Skeleton className="h-2 w-2 rounded-full" />
            ) : (
              <div
                className={`h-2 w-2 rounded-full ${
                  healthCheck ? "bg-green-500" : "bg-red-500"
                }`}
              />
            )}
            <span className="text-sm text-muted-foreground">
              {healthCheckLoading
                ? "Checking system status..."
                : healthCheck
                  ? "All systems operational"
                  : "System unavailable"}
            </span>
          </div>
        </CardContent>
      </Card>
    </div>
  );
}