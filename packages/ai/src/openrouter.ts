import { createOpenAI } from '@ai-sdk/openai';
import { generateObject, generateText } from 'ai';
import { z } from 'zod';

// OpenRouter client setup
const openrouter = createOpenAI({
  apiKey: process.env.OPENROUTER_API_KEY || '',
  baseURL: 'https://openrouter.ai/api/v1',
});

// Model configurations
export const models = {
  geminiFlash: openrouter('google/gemini-2.0-flash-exp:free'),
  geminiPro: openrouter('google/gemini-exp-1206'),
  claude35Sonnet: openrouter('anthropic/claude-3.5-sonnet'),
  gpt4o: openrouter('openai/gpt-4o'),
  gpt4oMini: openrouter('openai/gpt-4o-mini'),
} as const;

// Model selection based on task complexity and requirements
export const getModel = (task: 'simple' | 'complex' | 'reasoning' = 'simple') => {
  switch (task) {
    case 'simple':
      return models.geminiFlash;
    case 'complex':
      return models.geminiPro;
    case 'reasoning':
      return models.claude35Sonnet;
    default:
      return models.geminiFlash;
  }
};

// Enhanced text generation with retry logic
export async function generateTextWithRetry(
  prompt: string,
  systemPrompt?: string,
  options: {
    model?: 'simple' | 'complex' | 'reasoning';
    maxTokens?: number;
    temperature?: number;
    retries?: number;
  } = {}
): Promise<{ text: string; usage?: any; error?: string }> {
  const {
    model = 'simple',
    maxTokens = 2000,
    temperature = 0.7,
    retries = 3,
  } = options;

  let lastError: Error | null = null;

  for (let attempt = 0; attempt < retries; attempt++) {
    try {
      console.log(`🤖 Generating text (attempt ${attempt + 1}/${retries})`);

      const result = await generateText({
        model: getModel(model),
        messages: [
          ...(systemPrompt ? [{ role: 'system' as const, content: systemPrompt }] : []),
          { role: 'user' as const, content: prompt },
        ],
        maxTokens,
        temperature,
      });

      console.log(`✅ Text generation successful`);
      return {
        text: result.text,
        usage: result.usage,
      };
    } catch (error) {
      lastError = error as Error;
      console.error(`❌ Text generation failed (attempt ${attempt + 1}):`, error);

      if (attempt < retries - 1) {
        // Wait before retry with exponential backoff
        await new Promise(resolve => setTimeout(resolve, Math.pow(2, attempt) * 1000));
      }
    }
  }

  return {
    text: '',
    error: lastError?.message || 'Text generation failed after all retries',
  };
}

// Structured object generation with validation
export async function generateStructuredData<T>(
  prompt: string,
  schema: z.ZodSchema<T>,
  systemPrompt?: string,
  options: {
    model?: 'simple' | 'complex' | 'reasoning';
    retries?: number;
  } = {}
): Promise<{ data?: T; error?: string }> {
  const { model = 'complex', retries = 3 } = options;

  let lastError: Error | null = null;

  for (let attempt = 0; attempt < retries; attempt++) {
    try {
      console.log(`🤖 Generating structured data (attempt ${attempt + 1}/${retries})`);

      const result = await generateObject({
        model: getModel(model),
        messages: [
          ...(systemPrompt ? [{ role: 'system' as const, content: systemPrompt }] : []),
          { role: 'user' as const, content: prompt },
        ],
        schema,
      });

      console.log(`✅ Structured data generation successful`);
      return { data: result.object };
    } catch (error) {
      lastError = error as Error;
      console.error(`❌ Structured data generation failed (attempt ${attempt + 1}):`, error);

      if (attempt < retries - 1) {
        await new Promise(resolve => setTimeout(resolve, Math.pow(2, attempt) * 1000));
      }
    }
  }

  return {
    error: lastError?.message || 'Structured data generation failed after all retries',
  };
}