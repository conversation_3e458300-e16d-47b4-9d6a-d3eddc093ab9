import { z } from 'zod';

// Project Identification Schema
export const ProjectIdentificationSchema = z.object({
  projectName: z.string(),
  website: z.string().url().optional(),
  category: z.string(),
  description: z.string(),
  blockchains: z.array(z.string()),
  contractAddresses: z.record(z.string()).optional(),
  socialLinks: z.object({
    twitter: z.string().optional(),
    discord: z.string().optional(),
    telegram: z.string().optional(),
    github: z.string().optional(),
  }).optional(),
  confidence: z.number().min(1).max(10),
  alternatives: z.array(z.object({
    name: z.string(),
    reason: z.string(),
  })).optional(),
});

export type ProjectIdentification = z.infer<typeof ProjectIdentificationSchema>;

// Research Plan Schema
export const ResearchPlanSchema = z.object({
  analysisScope: z.object({
    onchainAnalytics: z.object({
      priority: z.enum(['high', 'medium', 'low']),
      tasks: z.array(z.string()),
    }),
    socialSentiment: z.object({
      priority: z.enum(['high', 'medium', 'low']),
      tasks: z.array(z.string()),
    }),
    competitorAnalysis: z.object({
      priority: z.enum(['high', 'medium', 'low']),
      tasks: z.array(z.string()),
    }),
    technicalAssessment: z.object({
      priority: z.enum(['high', 'medium', 'low']),
      tasks: z.array(z.string()),
    }),
    tokenomicsAnalysis: z.object({
      priority: z.enum(['high', 'medium', 'low']),
      tasks: z.array(z.string()),
    }),
    marketPositioning: z.object({
      priority: z.enum(['high', 'medium', 'low']),
      tasks: z.array(z.string()),
    }),
  }),
  dataSources: z.array(z.string()),
  successCriteria: z.array(z.string()),
  estimatedDuration: z.string(),
  riskFactors: z.array(z.string()),
});

export type ResearchPlan = z.infer<typeof ResearchPlanSchema>;

// Detailed Agent Result Schema (different from the basic one in types.ts)
export const DetailedAgentResultSchema = z.object({
  summary: z.string(),
  keyFindings: z.array(z.string()),
  metrics: z.record(z.any()),
  recommendations: z.array(z.string()),
  sources: z.array(z.string()),
  confidence: z.number().min(0).max(1),
  dataQuality: z.enum(['high', 'medium', 'low']),
  limitations: z.array(z.string()).optional(),
});

export type DetailedAgentResult = z.infer<typeof DetailedAgentResultSchema>;

// On-chain Analytics Specific Schema
export const OnChainAnalyticsSchema = DetailedAgentResultSchema.extend({
  metrics: z.object({
    tvl: z.object({
      current: z.number(),
      change24h: z.number(),
      change7d: z.number(),
      change30d: z.number(),
    }).optional(),
    volume: z.object({
      daily: z.number(),
      weekly: z.number(),
      monthly: z.number(),
    }).optional(),
    users: z.object({
      dau: z.number().optional(),
      mau: z.number().optional(),
      totalUsers: z.number().optional(),
    }).optional(),
    fees: z.object({
      daily: z.number(),
      weekly: z.number(),
      monthly: z.number(),
    }).optional(),
    tokenMetrics: z.object({
      marketCap: z.number().optional(),
      circulatingSupply: z.number().optional(),
      totalSupply: z.number().optional(),
      price: z.number().optional(),
    }).optional(),
  }),
});

export type OnChainAnalytics = z.infer<typeof OnChainAnalyticsSchema>;

// Social Sentiment Specific Schema
export const SocialSentimentSchema = DetailedAgentResultSchema.extend({
  metrics: z.object({
    twitter: z.object({
      followers: z.number(),
      followersGrowth: z.number(),
      engagementRate: z.number(),
      mentionVolume: z.number(),
      sentimentScore: z.number().min(-1).max(1),
    }).optional(),
    discord: z.object({
      members: z.number(),
      activeMembers: z.number(),
      messageVolume: z.number(),
      growthRate: z.number(),
    }).optional(),
    github: z.object({
      stars: z.number(),
      forks: z.number(),
      contributors: z.number(),
      commitActivity: z.number(),
    }).optional(),
    overallSentiment: z.number().min(-1).max(1),
  }),
});

export type SocialSentiment = z.infer<typeof SocialSentimentSchema>;

// Competitor Analysis Specific Schema
export const CompetitorAnalysisSchema = DetailedAgentResultSchema.extend({
  competitors: z.array(z.object({
    name: z.string(),
    category: z.string(),
    marketShare: z.number().optional(),
    strengths: z.array(z.string()),
    weaknesses: z.array(z.string()),
    differentiators: z.array(z.string()),
  })),
  marketPosition: z.object({
    rank: z.number().optional(),
    marketShare: z.number().optional(),
    competitiveAdvantages: z.array(z.string()),
    threats: z.array(z.string()),
  }),
});

export type CompetitorAnalysis = z.infer<typeof CompetitorAnalysisSchema>;

// Technical Assessment Specific Schema
export const TechnicalAssessmentSchema = DetailedAgentResultSchema.extend({
  metrics: z.object({
    securityScore: z.number().min(0).max(10),
    auditStatus: z.enum(['audited', 'partially_audited', 'unaudited']),
    codeQuality: z.number().min(0).max(10),
    innovationScore: z.number().min(0).max(10),
    developmentActivity: z.number().min(0).max(10),
  }),
  securityAssessment: z.object({
    audits: z.array(z.object({
      auditor: z.string(),
      date: z.string(),
      findings: z.number(),
      severity: z.enum(['low', 'medium', 'high', 'critical']),
    })),
    bugBounties: z.boolean(),
    incidents: z.array(z.object({
      date: z.string(),
      description: z.string(),
      impact: z.string(),
    })),
  }),
});

export type TechnicalAssessment = z.infer<typeof TechnicalAssessmentSchema>;

// Tokenomics Analysis Specific Schema
export const TokenomicsAnalysisSchema = DetailedAgentResultSchema.extend({
  tokenMetrics: z.object({
    totalSupply: z.number(),
    circulatingSupply: z.number(),
    inflationRate: z.number(),
    burnRate: z.number().optional(),
    stakingRatio: z.number().optional(),
  }),
  distribution: z.object({
    team: z.number(),
    investors: z.number(),
    community: z.number(),
    treasury: z.number(),
    ecosystem: z.number(),
  }),
  utility: z.array(z.string()),
  valueAccrual: z.array(z.string()),
  sustainabilityScore: z.number().min(0).max(10),
});

export type TokenomicsAnalysis = z.infer<typeof TokenomicsAnalysisSchema>;

// Market Positioning Specific Schema
export const MarketPositioningSchema = DetailedAgentResultSchema.extend({
  marketSize: z.object({
    tam: z.number(),
    sam: z.number(),
    som: z.number(),
  }),
  trends: z.array(z.object({
    trend: z.string(),
    impact: z.enum(['positive', 'negative', 'neutral']),
    timeframe: z.string(),
  })),
  opportunities: z.array(z.object({
    opportunity: z.string(),
    priority: z.enum(['high', 'medium', 'low']),
    timeframe: z.string(),
  })),
  risks: z.array(z.object({
    risk: z.string(),
    probability: z.enum(['high', 'medium', 'low']),
    impact: z.enum(['high', 'medium', 'low']),
  })),
});

export type MarketPositioning = z.infer<typeof MarketPositioningSchema>;

// Utility functions for parsing and validation
export function parseProjectIdentification(data: unknown): ProjectIdentification {
  return ProjectIdentificationSchema.parse(data);
}

export function parseResearchPlan(data: unknown): ResearchPlan {
  return ResearchPlanSchema.parse(data);
}

export function parseDetailedAgentResult(data: unknown): DetailedAgentResult {
  return DetailedAgentResultSchema.parse(data);
}

export function parseOnChainAnalytics(data: unknown): OnChainAnalytics {
  return OnChainAnalyticsSchema.parse(data);
}

export function parseSocialSentiment(data: unknown): SocialSentiment {
  return SocialSentimentSchema.parse(data);
}

export function parseCompetitorAnalysis(data: unknown): CompetitorAnalysis {
  return CompetitorAnalysisSchema.parse(data);
}

export function parseTechnicalAssessment(data: unknown): TechnicalAssessment {
  return TechnicalAssessmentSchema.parse(data);
}

export function parseTokenomicsAnalysis(data: unknown): TokenomicsAnalysis {
  return TokenomicsAnalysisSchema.parse(data);
}

export function parseMarketPositioning(data: unknown): MarketPositioning {
  return MarketPositioningSchema.parse(data);
}

// Safe parsing with error handling
export function safeParseDetailedAgentResult(data: unknown): { success: boolean; data?: DetailedAgentResult; error?: string } {
  try {
    const parsed = parseDetailedAgentResult(data);
    return { success: true, data: parsed };
  } catch (error) {
    return { 
      success: false, 
      error: error instanceof Error ? error.message : 'Unknown parsing error' 
    };
  }
}
