import { supabase } from '@cma/database';
import { RealtimeChannel } from '@supabase/supabase-js';
import { z } from 'zod';

const ProgressUpdateSchema = z.object({
  report_id: z.string(),
  agent_name: z.string(),
  step: z.string(),
  progress: z.number().min(0).max(100),
  status: z.enum(['pending', 'running', 'completed', 'failed']),
  message: z.string().optional(),
  metadata: z.record(z.any()).optional(),
  timestamp: z.string(),
});

export type ProgressUpdate = z.infer<typeof ProgressUpdateSchema>;

export interface RealtimeSubscription {
  channel: RealtimeChannel;
  unsubscribe: () => void;
}

export interface ProgressTrackingOptions {
  trackAllAgents?: boolean;
  trackSpecificAgents?: string[];
  includeDetailedLogs?: boolean;
  batchUpdates?: boolean;
  batchInterval?: number;
}

export class RealtimeProgressTracker {
  private channels: Map<string, RealtimeChannel> = new Map();
  private progressBatches: Map<string, ProgressUpdate[]> = new Map();
  private batchTimers: Map<string, NodeJS.Timeout> = new Map();
  private defaultBatchInterval = 1000; // 1 second

  // Subscribe to progress updates for a specific report
  subscribeToReportProgress(
    reportId: string,
    callback: (update: ProgressUpdate) => void,
    options: ProgressTrackingOptions = {}
  ): RealtimeSubscription {
    const channelName = `report_progress:${reportId}`;
    
    // Create channel if it doesn't exist
    if (!this.channels.has(channelName)) {
      const channel = supabase.channel(channelName);
      this.channels.set(channelName, channel);
    }
    
    const channel = this.channels.get(channelName)!;
    
    // Set up subscription
    channel
      .on(
        'postgres_changes',
        {
          event: '*',
          schema: 'public',
          table: 'agent_progress',
          filter: `report_id=eq.${reportId}`,
        },
        (payload) => {
          try {
            const update = this.parseProgressUpdate(payload.new);
            
            // Filter by agent if specified
            if (options.trackSpecificAgents && 
                !options.trackSpecificAgents.includes(update.agent_name)) {
              return;
            }
            
            callback(update);
          } catch (error) {
            console.error('Error parsing progress update:', error);
          }
        }
      )
      .subscribe();
    
    return {
      channel,
      unsubscribe: () => {
        channel.unsubscribe();
        this.channels.delete(channelName);
      },
    };
  }

  // Subscribe to all reports for a workspace/user
  subscribeToWorkspaceProgress(
    workspaceId: string,
    callback: (update: ProgressUpdate & { report_id: string }) => void,
    options: ProgressTrackingOptions = {}
  ): RealtimeSubscription {
    const channelName = `workspace_progress:${workspaceId}`;
    
    if (!this.channels.has(channelName)) {
      const channel = supabase.channel(channelName);
      this.channels.set(channelName, channel);
    }
    
    const channel = this.channels.get(channelName)!;
    
    channel
      .on(
        'postgres_changes',
        {
          event: '*',
          schema: 'public',
          table: 'agent_progress',
          filter: `workspace_id=eq.${workspaceId}`,
        },
        (payload) => {
          try {
            const update = this.parseProgressUpdate(payload.new);
            callback({ ...update, report_id: payload.new.report_id });
          } catch (error) {
            console.error('Error parsing workspace progress update:', error);
          }
        }
      )
      .subscribe();
    
    return {
      channel,
      unsubscribe: () => {
        channel.unsubscribe();
        this.channels.delete(channelName);
      },
    };
  }

  // Publish progress update
  async publishProgress(
    reportId: string,
    agentName: string,
    step: string,
    progress: number,
    status: 'pending' | 'running' | 'completed' | 'failed',
    message?: string,
    metadata?: Record<string, any>,
    options: ProgressTrackingOptions = {}
  ): Promise<void> {
    const update: ProgressUpdate = {
      report_id: reportId,
      agent_name: agentName,
      step,
      progress: Math.max(0, Math.min(100, progress)),
      status,
      message,
      metadata,
      timestamp: new Date().toISOString(),
    };
    
    try {
      // Validate the update
      ProgressUpdateSchema.parse(update);
      
      if (options.batchUpdates) {
        await this.batchProgressUpdate(reportId, update, options);
      } else {
        await this.sendProgressUpdate(update);
      }
    } catch (error) {
      console.error('Error publishing progress:', error);
      throw new Error('Failed to publish progress update');
    }
  }

  // Batch progress updates to reduce database load
  private async batchProgressUpdate(
    reportId: string,
    update: ProgressUpdate,
    options: ProgressTrackingOptions
  ): Promise<void> {
    const batchKey = `${reportId}_${update.agent_name}`;
    
    // Initialize batch array if needed
    if (!this.progressBatches.has(batchKey)) {
      this.progressBatches.set(batchKey, []);
    }
    
    // Add update to batch
    this.progressBatches.get(batchKey)!.push(update);
    
    // Clear existing timer
    if (this.batchTimers.has(batchKey)) {
      clearTimeout(this.batchTimers.get(batchKey)!);
    }
    
    // Set new timer
    const interval = options.batchInterval || this.defaultBatchInterval;
    const timer = setTimeout(async () => {
      await this.flushProgressBatch(batchKey);
    }, interval);
    
    this.batchTimers.set(batchKey, timer);
  }

  private async flushProgressBatch(batchKey: string): Promise<void> {
    const batch = this.progressBatches.get(batchKey);
    if (!batch || batch.length === 0) return;
    
    try {
      // Send the latest update from the batch
      const latestUpdate = batch[batch.length - 1];
      await this.sendProgressUpdate(latestUpdate);
      
      // Clear the batch
      this.progressBatches.delete(batchKey);
      this.batchTimers.delete(batchKey);
    } catch (error) {
      console.error('Error flushing progress batch:', error);
    }
  }

  private async sendProgressUpdate(update: ProgressUpdate): Promise<void> {
    const { error } = await supabase
      .from('agent_progress')
      .upsert({
        report_id: update.report_id,
        agent_name: update.agent_name,
        step: update.step,
        progress: update.progress,
        status: update.status,
        message: update.message,
        metadata: update.metadata || {},
        updated_at: update.timestamp,
      }, {
        onConflict: 'report_id,agent_name',
      });
    
    if (error) {
      console.error('Error sending progress update:', error);
      throw new Error('Failed to send progress update');
    }
  }

  private parseProgressUpdate(data: any): ProgressUpdate {
    return {
      report_id: data.report_id,
      agent_name: data.agent_name,
      step: data.step,
      progress: data.progress,
      status: data.status,
      message: data.message,
      metadata: data.metadata || {},
      timestamp: data.updated_at,
    };
  }

  // Get current progress for a report
  async getReportProgress(reportId: string): Promise<ProgressUpdate[]> {
    const { data, error } = await supabase
      .from('agent_progress')
      .select('*')
      .eq('report_id', reportId)
      .order('updated_at', { ascending: true });
    
    if (error) {
      throw new Error(`Failed to get report progress: ${error.message}`);
    }
    
    return (data || []).map(item => this.parseProgressUpdate(item));
  }

  // Get overall progress summary for a report
  async getReportSummary(reportId: string): Promise<{
    overall_progress: number;
    completed_agents: number;
    total_agents: number;
    status: 'pending' | 'running' | 'completed' | 'failed';
    estimated_completion?: string;
  }> {
    const progress = await this.getReportProgress(reportId);
    
    if (progress.length === 0) {
      return {
        overall_progress: 0,
        completed_agents: 0,
        total_agents: 0,
        status: 'pending',
      };
    }
    
    const totalAgents = progress.length;
    const completedAgents = progress.filter(p => p.status === 'completed').length;
    const failedAgents = progress.filter(p => p.status === 'failed').length;
    const overallProgress = progress.reduce((sum, p) => sum + p.progress, 0) / totalAgents;
    
    let status: 'pending' | 'running' | 'completed' | 'failed' = 'running';
    
    if (completedAgents === totalAgents) {
      status = 'completed';
    } else if (failedAgents > 0) {
      status = 'failed';
    } else if (overallProgress === 0) {
      status = 'pending';
    }
    
    const summary = {
      overall_progress: Math.round(overallProgress),
      completed_agents: completedAgents,
      total_agents: totalAgents,
      status,
    };
    
    // Estimate completion time
    if (status === 'running' && overallProgress > 0) {
      const avgTimePerPercent = this.estimateTimePerPercent(progress);
      const remainingProgress = 100 - overallProgress;
      const estimatedMs = remainingProgress * avgTimePerPercent;
      const estimatedCompletion = new Date(Date.now() + estimatedMs).toISOString();
      
      return { ...summary, estimated_completion: estimatedCompletion };
    }
    
    return summary;
  }

  private estimateTimePerPercent(progress: ProgressUpdate[]): number {
    // Simple estimation based on current progress rate
    const runningAgents = progress.filter(p => p.status === 'running');
    if (runningAgents.length === 0) return 30000; // Default 30 seconds per percent
    
    const avgProgress = runningAgents.reduce((sum, p) => sum + p.progress, 0) / runningAgents.length;
    const latestUpdate = Math.max(...runningAgents.map(p => new Date(p.timestamp).getTime()));
    const oldestUpdate = Math.min(...runningAgents.map(p => new Date(p.timestamp).getTime()));
    
    const timeElapsed = latestUpdate - oldestUpdate;
    
    if (avgProgress > 0 && timeElapsed > 0) {
      return timeElapsed / avgProgress;
    }
    
    return 30000; // Default fallback
  }

  // Clean up old progress records
  async cleanupOldProgress(daysOld: number = 7): Promise<number> {
    const cutoffDate = new Date(Date.now() - daysOld * 24 * 60 * 60 * 1000).toISOString();
    
    const { data, error } = await supabase
      .from('agent_progress')
      .delete()
      .lt('updated_at', cutoffDate)
      .select('report_id');
    
    if (error) {
      throw new Error(`Failed to cleanup old progress: ${error.message}`);
    }
    
    return data?.length || 0;
  }

  // Subscribe to agent-specific progress across all reports
  subscribeToAgentProgress(
    agentName: string,
    callback: (update: ProgressUpdate) => void
  ): RealtimeSubscription {
    const channelName = `agent_progress:${agentName}`;
    
    if (!this.channels.has(channelName)) {
      const channel = supabase.channel(channelName);
      this.channels.set(channelName, channel);
    }
    
    const channel = this.channels.get(channelName)!;
    
    channel
      .on(
        'postgres_changes',
        {
          event: '*',
          schema: 'public',
          table: 'agent_progress',
          filter: `agent_name=eq.${agentName}`,
        },
        (payload) => {
          try {
            const update = this.parseProgressUpdate(payload.new);
            callback(update);
          } catch (error) {
            console.error('Error parsing agent progress update:', error);
          }
        }
      )
      .subscribe();
    
    return {
      channel,
      unsubscribe: () => {
        channel.unsubscribe();
        this.channels.delete(channelName);
      },
    };
  }

  // Broadcast custom events
  async broadcastEvent(
    reportId: string,
    eventType: string,
    payload: Record<string, any>
  ): Promise<void> {
    const channelName = `report_events:${reportId}`;
    const channel = supabase.channel(channelName);
    
    await channel.send({
      type: 'broadcast',
      event: eventType,
      payload: {
        ...payload,
        timestamp: new Date().toISOString(),
      },
    });
  }

  // Subscribe to custom events
  subscribeToEvents(
    reportId: string,
    eventType: string,
    callback: (payload: any) => void
  ): RealtimeSubscription {
    const channelName = `report_events:${reportId}`;
    
    if (!this.channels.has(channelName)) {
      const channel = supabase.channel(channelName);
      this.channels.set(channelName, channel);
    }
    
    const channel = this.channels.get(channelName)!;
    
    channel
      .on('broadcast', { event: eventType }, (payload) => {
        callback(payload.payload);
      })
      .subscribe();
    
    return {
      channel,
      unsubscribe: () => {
        channel.unsubscribe();
        this.channels.delete(channelName);
      },
    };
  }

  // Flush all pending batches
  async flushAllBatches(): Promise<void> {
    const flushPromises = Array.from(this.progressBatches.keys()).map(batchKey =>
      this.flushProgressBatch(batchKey)
    );
    
    await Promise.allSettled(flushPromises);
  }

  // Cleanup method
  async cleanup(): Promise<void> {
    // Flush all pending batches
    await this.flushAllBatches();
    
    // Clear all timers
    this.batchTimers.forEach(timer => clearTimeout(timer));
    this.batchTimers.clear();
    
    // Unsubscribe from all channels
    this.channels.forEach(channel => channel.unsubscribe());
    this.channels.clear();
    
    // Clear batches
    this.progressBatches.clear();
  }

  // Get active subscriptions count
  getActiveSubscriptionsCount(): number {
    return this.channels.size;
  }

  // Get statistics
  async getProgressStats(reportId?: string): Promise<{
    total_updates: number;
    active_agents: number;
    completed_agents: number;
    failed_agents: number;
    avg_progress: number;
  }> {
    let query = supabase
      .from('agent_progress')
      .select('*');
    
    if (reportId) {
      query = query.eq('report_id', reportId);
    }
    
    const { data, error } = await query;
    
    if (error) {
      throw new Error(`Failed to get progress stats: ${error.message}`);
    }
    
    const updates = data || [];
    const activeAgents = updates.filter(u => u.status === 'running').length;
    const completedAgents = updates.filter(u => u.status === 'completed').length;
    const failedAgents = updates.filter(u => u.status === 'failed').length;
    const avgProgress = updates.length > 0 
      ? updates.reduce((sum, u) => sum + u.progress, 0) / updates.length 
      : 0;
    
    return {
      total_updates: updates.length,
      active_agents: activeAgents,
      completed_agents: completedAgents,
      failed_agents: failedAgents,
      avg_progress: Math.round(avgProgress),
    };
  }
}

// Singleton instance
export const realtimeTracker = new RealtimeProgressTracker();

// Helper functions for easy use in agents

export async function updateAgentProgress(
  reportId: string,
  agentName: string,
  step: string,
  progress: number,
  status: 'pending' | 'running' | 'completed' | 'failed',
  message?: string,
  metadata?: Record<string, any>
): Promise<void> {
  return realtimeTracker.publishProgress(
    reportId,
    agentName,
    step,
    progress,
    status,
    message,
    metadata
  );
}

export function subscribeToProgress(
  reportId: string,
  callback: (update: ProgressUpdate) => void,
  options?: ProgressTrackingOptions
): RealtimeSubscription {
  return realtimeTracker.subscribeToReportProgress(reportId, callback, options);
}

export async function getProgressSummary(reportId: string) {
  return realtimeTracker.getReportSummary(reportId);
}